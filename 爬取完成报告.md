# 学术桥教育部第四轮学科评估数据爬取完成报告

## 爬取概况

✅ **爬取状态**: 成功完成  
🌐 **目标网站**: https://www.acabridge.cn/acabridge/aca_web/xkpg4/index.html  
📅 **爬取时间**: 2025年7月3日  
⏱️ **耗时**: 约8分钟  

## 数据统计

### 总体数据
- **总数据条数**: 5,176 条
- **学科门类数量**: 12 个
- **一级学科数量**: 94 个
- **参评高校数量**: 476 所

### 学科门类分布
| 学科门类 | 数据条数 |
|---------|---------|
| 工学 | 1,931 条 |
| 理学 | 730 条 |
| 管理学 | 465 条 |
| 法学 | 392 条 |
| 医学 | 390 条 |
| 文学 | 274 条 |
| 艺术学 | 249 条 |
| 农学 | 223 条 |
| 经济学 | 176 条 |
| 教育学 | 161 条 |
| 历史学 | 124 条 |
| 哲学 | 61 条 |

### 评估结果分布
| 评估等级 | 数量 |
|---------|------|
| C+ | 765 条 |
| B | 740 条 |
| B+ | 738 条 |
| C- | 735 条 |
| B- | 730 条 |
| C | 726 条 |
| A- | 350 条 |
| A+ | 230 条 |
| A | 162 条 |

### 顶尖高校（按学科数量排名）
1. **浙江大学**: 59 个学科
2. **四川大学**: 58 个学科
3. **吉林大学**: 56 个学科
4. **清华大学**: 52 个学科
5. **武汉大学**: 51 个学科
6. **厦门大学**: 50 个学科
7. **上海交通大学**: 50 个学科
8. **北京大学**: 49 个学科
9. **中国科学院大学**: 48 个学科
10. **中山大学**: 48 个学科

### 地区分布（前15名）
| 地区 | 记录数 |
|------|-------|
| 北京 | 655 条 |
| 江苏 | 430 条 |
| 上海 | 357 条 |
| 湖北 | 275 条 |
| 陕西 | 263 条 |
| 广东 | 254 条 |
| 辽宁 | 246 条 |
| 山东 | 235 条 |
| 浙江 | 212 条 |
| 四川 | 202 条 |
| 湖南 | 198 条 |
| 黑龙江 | 160 条 |
| 河南 | 157 条 |
| 天津 | 150 条 |
| 吉林 | 150 条 |

## 技术实现

### 爬虫特点
- 使用 **Selenium** 处理动态JavaScript页面
- 自动识别学科门类和具体学科的层级结构
- 智能等待页面加载，确保数据完整性
- 支持断点续爬和错误恢复

### 数据字段
每条记录包含以下字段：
- **序号**: 排序编号
- **单位名称**: 高校名称
- **所在地区**: 高校所在省市
- **学科门类**: 学科大类（如工学、理学等）
- **一级学科**: 具体学科名称
- **评估结果**: 评估等级（A+、A、A-、B+、B、B-、C+、C、C-）
- **排名占比**: 排名百分比范围
- **查询学科**: 爬取时的查询条件

## 输出文件

### 1. CSV格式数据
- **文件名**: `acabridge_subject_evaluation.csv`
- **编码**: UTF-8 with BOM
- **用途**: 适合Excel打开和数据分析

### 2. JSON格式数据
- **文件名**: `acabridge_subject_evaluation.json`
- **编码**: UTF-8
- **用途**: 适合程序处理和API接口

### 3. 分析报告
- **文件名**: `data_analysis_summary.txt`
- **内容**: 数据统计摘要

### 4. 爬取日志
- **文件名**: `acabridge_crawler.log`
- **内容**: 详细的爬取过程记录

## 数据质量

✅ **完整性**: 覆盖所有12个学科门类的94个一级学科  
✅ **准确性**: 数据直接来源于官方网站，保证准确性  
✅ **时效性**: 2025年7月3日最新爬取  
✅ **结构化**: 数据格式规范，便于后续处理  

## 使用建议

1. **数据分析**: 可用于学科排名分析、高校实力评估
2. **可视化**: 支持制作各类图表和报表
3. **数据库导入**: 可直接导入MySQL、PostgreSQL等数据库
4. **API开发**: JSON格式便于构建查询API

## 技术说明

- **开发语言**: Python 3
- **主要依赖**: Selenium, BeautifulSoup4, pandas
- **浏览器**: Chrome WebDriver
- **运行环境**: Windows 10/11

---

**爬取完成时间**: 2025年7月3日 16:43  
**数据有效性**: 基于教育部第四轮学科评估结果  
**数据来源**: 学术桥官方网站
