#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
教育部第四轮学科评估数据爬虫 - Selenium版本
使用Selenium处理动态加载的网页
"""

import time
import csv
import json
import logging
from typing import List, Dict, Any
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('selenium_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SeleniumCrawler:
    def __init__(self):
        self.base_url = "https://www.cingta.com"
        self.driver = None
        self.all_data = []
        
        # 学科分类列表
        self.subject_categories = [
            '哲学', '经济学', '法学', '教育学', '文学', '历史学',
            '理学', '工学', '农学', '医学', '管理学', '艺术学', '交叉学科'
        ]
        
    def setup_driver(self):
        """
        设置Chrome浏览器驱动
        """
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # 使用无头模式
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--disable-features=VizDisplayCompositor')
            chrome_options.add_argument('--user-data-dir=C:\\temp\\chrome_user_data')  # 指定用户数据目录
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            logger.info("Chrome驱动初始化成功")

        except Exception as e:
            logger.error(f"Chrome驱动初始化失败: {str(e)}")
            raise
            
    def close_driver(self):
        """
        关闭浏览器驱动
        """
        if self.driver:
            self.driver.quit()
            logger.info("浏览器驱动已关闭")
    
    def wait_for_page_load(self, timeout: int = 20):
        """
        等待页面加载完成
        """
        try:
            # 等待表格出现
            WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((By.TAG_NAME, "table"))
            )
            time.sleep(3)  # 额外等待数据加载
            return True
        except TimeoutException:
            logger.warning("页面加载超时")
            return False
    
    def click_category_filter(self, category_name: str) -> bool:
        """
        点击学科分类筛选
        """
        try:
            logger.info(f"尝试点击分类: {category_name}")
            
            # 查找分类筛选区域
            filter_elements = self.driver.find_elements(By.XPATH, f"//div[contains(@class, 'filter') or contains(@class, 'category')]//span[contains(text(), '{category_name}')]")
            
            if not filter_elements:
                # 尝试其他选择器
                filter_elements = self.driver.find_elements(By.XPATH, f"//*[contains(text(), '{category_name}')]")
            
            for element in filter_elements:
                try:
                    if element.is_displayed() and element.is_enabled():
                        self.driver.execute_script("arguments[0].click();", element)
                        logger.info(f"成功点击分类: {category_name}")
                        time.sleep(3)  # 等待数据加载
                        return True
                except Exception as e:
                    continue
                    
            logger.warning(f"未找到可点击的分类: {category_name}")
            return False
            
        except Exception as e:
            logger.error(f"点击分类 {category_name} 失败: {str(e)}")
            return False
    
    def extract_table_data(self, category: str) -> List[Dict[str, Any]]:
        """
        提取当前页面的表格数据
        """
        try:
            # 等待表格加载
            if not self.wait_for_page_load():
                return []
            
            # 查找表格
            tables = self.driver.find_elements(By.TAG_NAME, "table")
            
            if not tables:
                logger.warning("未找到表格")
                return []
            
            data_list = []
            
            for table in tables:
                try:
                    # 获取表格行
                    rows = table.find_elements(By.TAG_NAME, "tr")
                    
                    if len(rows) <= 1:  # 只有表头或没有数据
                        continue
                    
                    # 获取表头
                    header_row = rows[0]
                    headers = [th.text.strip() for th in header_row.find_elements(By.TAG_NAME, "th")]
                    if not headers:
                        headers = [td.text.strip() for td in header_row.find_elements(By.TAG_NAME, "td")]
                    
                    logger.info(f"表头: {headers}")
                    
                    # 提取数据行
                    for i, row in enumerate(rows[1:], 1):
                        cells = row.find_elements(By.TAG_NAME, "td")
                        
                        if len(cells) >= 5:  # 确保有足够的列
                            data_item = {
                                '序号': cells[0].text.strip(),
                                '单位名称': cells[1].text.strip(),
                                '一级学科': cells[2].text.strip(),
                                '评估结果': cells[3].text.strip(),
                                '排名占比': cells[4].text.strip(),
                                '学科分类': category
                            }
                            
                            # 过滤空数据
                            if data_item['单位名称'] and data_item['一级学科']:
                                data_list.append(data_item)
                    
                    if data_list:
                        break  # 找到有效数据就退出
                        
                except Exception as e:
                    logger.error(f"处理表格异常: {str(e)}")
                    continue
            
            logger.info(f"从表格提取到 {len(data_list)} 条数据")
            return data_list
            
        except Exception as e:
            logger.error(f"提取表格数据异常: {str(e)}")
            return []
    
    def handle_pagination(self) -> bool:
        """
        处理分页，点击下一页
        """
        try:
            # 查找下一页按钮
            next_buttons = self.driver.find_elements(By.XPATH, "//a[contains(text(), '下一页') or contains(@class, 'next')]")
            
            for button in next_buttons:
                if button.is_enabled() and button.is_displayed():
                    button.click()
                    time.sleep(3)
                    return True
            
            # 查找页码按钮
            page_buttons = self.driver.find_elements(By.XPATH, "//a[contains(@class, 'page')]")
            current_page = None
            
            # 找到当前页码
            for button in page_buttons:
                if 'active' in button.get_attribute('class') or 'current' in button.get_attribute('class'):
                    try:
                        current_page = int(button.text.strip())
                        break
                    except:
                        continue
            
            # 点击下一页码
            if current_page:
                next_page = current_page + 1
                next_page_button = self.driver.find_elements(By.XPATH, f"//a[contains(@class, 'page') and text()='{next_page}']")
                if next_page_button and next_page_button[0].is_enabled():
                    next_page_button[0].click()
                    time.sleep(3)
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"翻页失败: {str(e)}")
            return False
    
    def crawl_category_data(self, category: str) -> List[Dict[str, Any]]:
        """
        爬取指定分类的所有数据
        """
        logger.info(f"开始爬取分类: {category}")
        category_data = []
        page = 1
        
        try:
            # 点击分类筛选
            if not self.click_category_filter(category):
                logger.warning(f"无法选择分类 {category}，尝试获取当前页面数据")
            
            while True:
                logger.info(f"正在爬取分类 {category} 第 {page} 页")
                
                # 提取当前页面数据
                page_data = self.extract_table_data(category)
                
                if not page_data:
                    logger.info(f"分类 {category} 第 {page} 页无数据")
                    break
                
                category_data.extend(page_data)
                logger.info(f"分类 {category} 第 {page} 页获取 {len(page_data)} 条数据")
                
                # 尝试翻页
                if not self.handle_pagination():
                    logger.info(f"分类 {category} 无更多页面")
                    break
                
                page += 1
                
                # 防止无限循环
                if page > 50:
                    logger.warning(f"分类 {category} 页数过多，停止爬取")
                    break
                    
        except Exception as e:
            logger.error(f"爬取分类 {category} 异常: {str(e)}")
        
        logger.info(f"分类 {category} 共获取 {len(category_data)} 条数据")
        return category_data
    
    def crawl_all_data(self):
        """
        爬取所有分类的数据
        """
        logger.info("开始爬取教育部第四轮学科评估数据")
        
        try:
            # 初始化浏览器
            self.setup_driver()
            
            # 访问主页面
            main_url = f"{self.base_url}/opendata/detail/191?name=教育部第四轮学科评估数据"
            logger.info(f"访问页面: {main_url}")
            self.driver.get(main_url)
            
            # 等待页面加载
            time.sleep(5)
            
            # 首先尝试获取默认显示的数据（通常是第一个分类）
            default_data = self.extract_table_data("默认")
            if default_data:
                self.all_data.extend(default_data)
                logger.info(f"获取默认数据: {len(default_data)} 条")
            
            # 遍历所有分类
            for category in self.subject_categories:
                try:
                    category_data = self.crawl_category_data(category)
                    
                    # 去重处理
                    new_data = []
                    for item in category_data:
                        # 简单去重：检查单位名称和学科是否已存在
                        is_duplicate = False
                        for existing_item in self.all_data:
                            if (existing_item.get('单位名称') == item.get('单位名称') and 
                                existing_item.get('一级学科') == item.get('一级学科')):
                                is_duplicate = True
                                break
                        
                        if not is_duplicate:
                            new_data.append(item)
                    
                    self.all_data.extend(new_data)
                    logger.info(f"分类 {category} 新增 {len(new_data)} 条数据，累计: {len(self.all_data)} 条")
                    
                    time.sleep(2)  # 分类间休息
                    
                except Exception as e:
                    logger.error(f"爬取分类 {category} 失败: {str(e)}")
                    continue
                    
        except Exception as e:
            logger.error(f"爬取过程异常: {str(e)}")
        finally:
            self.close_driver()
        
        logger.info(f"爬取完成，总计获取: {len(self.all_data)} 条数据")
    
    def save_to_csv(self, filename: str = "subject_evaluation_selenium.csv"):
        """
        保存数据到CSV文件
        """
        if not self.all_data:
            logger.warning("没有数据可保存")
            return
            
        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = ['序号', '单位名称', '一级学科', '评估结果', '排名占比', '学科分类']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for item in self.all_data:
                    writer.writerow(item)
                    
            logger.info(f"数据已保存到 {filename}，共 {len(self.all_data)} 条记录")
            
        except Exception as e:
            logger.error(f"保存CSV文件失败: {str(e)}")
    
    def save_to_json(self, filename: str = "subject_evaluation_selenium.json"):
        """
        保存数据到JSON文件
        """
        if not self.all_data:
            logger.warning("没有数据可保存")
            return
            
        try:
            with open(filename, 'w', encoding='utf-8') as jsonfile:
                json.dump(self.all_data, jsonfile, ensure_ascii=False, indent=2)
                
            logger.info(f"数据已保存到 {filename}，共 {len(self.all_data)} 条记录")
            
        except Exception as e:
            logger.error(f"保存JSON文件失败: {str(e)}")

def main():
    """
    主函数
    """
    crawler = SeleniumCrawler()
    
    try:
        # 爬取所有数据
        crawler.crawl_all_data()
        
        # 保存数据
        crawler.save_to_csv()
        crawler.save_to_json()
        
        print(f"爬取完成！共获取 {len(crawler.all_data)} 条数据")
        
    except KeyboardInterrupt:
        logger.info("用户中断爬取")
        print("爬取被中断")
        
    except Exception as e:
        logger.error(f"爬取过程发生异常: {str(e)}")
        print(f"爬取失败: {str(e)}")

if __name__ == "__main__":
    main()
