# 教育部第四轮学科评估数据爬虫与排名系统

## 项目简介

本项目是一个完整的学科评估数据获取、处理和展示系统，包含数据爬虫、数据分析和Web展示功能。通过自动化技术从官方网站获取教育部第四轮学科评估数据，为教育研究、院校分析和学科排名提供全面的数据支持。

## 🏗️ 系统架构

- **数据爬虫**: Python爬虫自动获取学科评估数据
- **数据处理**: 数据清洗、分析和统计
- **后端服务**: Supabase作为数据库和API服务
- **前端展示**: Web界面展示排名和分析结果
- **部署平台**: Cloudflare Pages作为网站部署平台

## 🎯 项目特点

- **全面覆盖**: 爬取所有12个学科门类、94个一级学科的完整数据
- **智能识别**: 自动识别网站的层级结构，按学科门类和具体学科分类爬取
- **数据完整**: 包含高校名称、地区、学科、评估结果、排名占比等完整信息
- **多格式输出**: 同时生成CSV和JSON格式，满足不同使用需求
- **实时分析**: 内置数据分析功能，生成统计报告和可视化图表
- **Web界面**: 提供友好的查询和展示界面

## 📊 数据概览

### 最新爬取数据（2025-07-03）
- **总记录数**: 5,176 条
- **学科门类**: 12 个
- **一级学科**: 94 个
- **参评高校**: 476 所
- **数据来源**: [学术桥官方网站](https://www.acabridge.cn/acabridge/aca_web/xkpg4/index.html)

### 学科分布
| 学科门类 | 记录数 | 占比 |
|---------|-------|------|
| 工学 | 1,931 | 37.3% |
| 理学 | 730 | 14.1% |
| 管理学 | 465 | 9.0% |
| 法学 | 392 | 7.6% |
| 医学 | 390 | 7.5% |
| 文学 | 274 | 5.3% |
| 艺术学 | 249 | 4.8% |
| 农学 | 223 | 4.3% |
| 经济学 | 176 | 3.4% |
| 教育学 | 161 | 3.1% |
| 历史学 | 124 | 2.4% |
| 哲学 | 61 | 1.2% |

### 评估结果分布
| 评估等级 | 数量 | 占比 |
|---------|------|------|
| A+ | 230 | 4.4% |
| A | 162 | 3.1% |
| A- | 350 | 6.8% |
| B+ | 738 | 14.3% |
| B | 740 | 14.3% |
| B- | 730 | 14.1% |
| C+ | 765 | 14.8% |
| C | 726 | 14.0% |
| C- | 735 | 14.2% |

## 🛠️ 技术栈

### 数据爬虫
- **Python 3.7+**
- **Selenium**: 处理动态JavaScript页面
- **BeautifulSoup4**: HTML解析
- **Pandas**: 数据分析和处理
- **Chrome WebDriver**: 浏览器自动化

### 后端服务
- **Supabase**: PostgreSQL数据库 + 实时API
- **Python**: 数据处理脚本

### 前端展示
- **HTML/CSS/JavaScript**: 基础Web技术
- **Chart.js**: 数据可视化
- **Bootstrap**: UI框架

### 部署平台
- **Cloudflare Pages**: 静态网站托管
- **GitHub**: 代码版本控制

## 📦 安装依赖

### 爬虫环境
```bash
pip install selenium beautifulsoup4 pandas
```

### 数据处理环境
```bash
pip install supabase pandas python-dotenv
```

**注意**: 需要下载并配置Chrome WebDriver，确保Chrome浏览器已安装。

## 🚀 快速开始

### 1. 数据爬取

```bash
# 爬取学术桥数据
python acabridge_crawler.py

# 数据分析
python analyze_data.py
```

### 2. 数据导入

```bash
# 进入数据处理目录
cd 数据处理脚本

# 配置Supabase连接
cp .env.example .env
# 编辑.env文件，填入Supabase配置

# 创建数据表
python setup_supabase.py

# 导入数据
python import_data_to_supabase.py
```

### 3. 本地开发

```bash
# 启动本地服务器
python -m http.server 8000

# 访问 http://localhost:8000
```

## 📁 项目结构

```
学科排名系统/
├── 爬虫脚本/
│   ├── acabridge_crawler.py          # 学术桥爬虫
│   ├── analyze_data.py               # 数据分析
│   └── test_website_structure.py     # 网站结构测试
├── 数据处理脚本/
│   ├── setup_supabase.py             # 数据库初始化
│   ├── import_data_to_supabase.py    # 数据导入
│   └── requirements.txt              # Python依赖
├── 数据文件/
│   ├── acabridge_subject_evaluation.csv    # CSV格式数据
│   ├── acabridge_subject_evaluation.json   # JSON格式数据
│   └── data_analysis_summary.txt     # 分析摘要
├── 高考资料/                         # 参考资料
├── 日志文件/
│   └── acabridge_crawler.log         # 爬取日志
├── 爬取完成报告.md                   # 详细报告
└── README.md                         # 项目说明
```

## 📋 数据字段说明

| 字段名 | 类型 | 说明 |
|-------|------|------|
| 序号 | String | 排序编号 |
| 单位名称 | String | 高校名称 |
| 所在地区 | String | 高校所在省市 |
| 学科门类 | String | 学科大类（如工学、理学等） |
| 一级学科 | String | 具体学科名称 |
| 评估结果 | String | 评估等级（A+、A、A-、B+、B、B-、C+、C、C-） |
| 排名占比 | String | 排名百分比范围 |
| 查询学科 | String | 爬取时的查询条件 |

## 🎯 使用场景

### 1. 教育研究
- 学科发展趋势分析
- 高校实力评估
- 区域教育资源分布研究

### 2. 数据分析
- 学科排名可视化
- 高校对比分析
- 地区教育水平评估

### 3. 应用开发
- 教育信息查询系统
- 高考志愿填报参考
- 学术资源推荐平台

## 📈 数据示例

```csv
序号,单位名称,所在地区,学科门类,一级学科,评估结果,排名占比,查询学科
1,复旦大学,上海,哲学,哲学,A+,前2%或前2名,哲学-哲学
2,北京大学,北京,哲学,哲学,A+,前2%或前2名,哲学-哲学
3,南京大学,江苏,哲学,哲学,A,2%-5%,哲学-哲学
```

## 🏆 数据亮点

### 顶尖高校（按学科数量排名）
1. **浙江大学**: 59 个学科
2. **四川大学**: 58 个学科
3. **吉林大学**: 56 个学科
4. **清华大学**: 52 个学科
5. **武汉大学**: 51 个学科

### A+学科分布（前10名）
1. **计算机科学与技术**: 5个A+
2. **马克思主义理论**: 4个A+
3. **化学**: 4个A+
4. **生物学**: 4个A+
5. **机械工程**: 4个A+

### 地区分布（前5名）
1. **北京**: 655 条记录
2. **江苏**: 430 条记录
3. **上海**: 357 条记录
4. **湖北**: 275 条记录
5. **陕西**: 263 条记录

## ⚙️ 配置说明

### 爬虫配置
- 默认使用Chrome浏览器（可见模式，便于调试）
- 等待时间：页面加载3秒，学科切换2秒
- 自动重试机制：遇到错误会跳过并继续

### Supabase配置
在`数据处理脚本/.env`文件中配置：
```env
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_anon_key
```

### 修改配置
在相应脚本中可以调整：
- 浏览器选项（无头模式等）
- 等待时间
- 输出文件名
- 数据库表结构

## 🔧 故障排除

### 常见问题

1. **Chrome WebDriver错误**
   - 确保Chrome浏览器已安装
   - 下载对应版本的ChromeDriver
   - 将ChromeDriver添加到系统PATH

2. **网络连接问题**
   - 检查网络连接
   - 确认目标网站可访问
   - 考虑使用代理

3. **数据不完整**
   - 检查日志文件定位问题
   - 网站结构可能发生变化
   - 重新运行爬虫

4. **Supabase连接问题**
   - 检查.env文件配置
   - 确认Supabase项目状态
   - 验证API密钥权限

## 📝 更新日志

### v1.0.0 (2025-07-03)
- ✅ 完成学术桥网站数据爬取
- ✅ 支持所有学科门类自动识别
- ✅ 生成CSV和JSON格式数据
- ✅ 添加数据分析功能
- ✅ 完善日志记录和错误处理
- ✅ 集成Supabase数据库
- ✅ 创建Web展示界面

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目：

1. Fork本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## ⚖️ 免责声明

- 本项目仅用于学习和研究目的
- 请遵守网站的robots.txt和使用条款
- 数据来源于公开网站，请以官方发布为准
- 使用本工具产生的任何后果由使用者承担

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者

## 📄 许可证

本项目采用MIT许可证 - 查看[LICENSE](LICENSE)文件了解详情

---

**最后更新**: 2025年7月3日
**项目状态**: 活跃开发中
**数据来源**: [学术桥 - 教育部第四轮学科评估结果](https://www.acabridge.cn/acabridge/aca_web/xkpg4/index.html)
