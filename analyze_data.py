#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析爬取的学科评估数据
"""

import pandas as pd

def analyze_data():
    """分析数据"""
    try:
        # 读取CSV文件
        df = pd.read_csv('acabridge_subject_evaluation.csv')
        
        print("=" * 50)
        print("学术桥教育部第四轮学科评估数据分析")
        print("=" * 50)
        
        print(f"总数据条数: {len(df)}")
        print(f"学科门类数量: {df['学科门类'].nunique()}")
        print(f"一级学科数量: {df['一级学科'].nunique()}")
        print(f"高校数量: {df['单位名称'].nunique()}")
        
        print("\n" + "=" * 30)
        print("学科门类分布:")
        print("=" * 30)
        category_counts = df['学科门类'].value_counts()
        for category, count in category_counts.items():
            print(f"{category}: {count} 条")
        
        print("\n" + "=" * 30)
        print("评估结果分布:")
        print("=" * 30)
        result_counts = df['评估结果'].value_counts()
        for result, count in result_counts.items():
            print(f"{result}: {count} 条")
        
        print("\n" + "=" * 30)
        print("前10个高校（按数据条数）:")
        print("=" * 30)
        university_counts = df['单位名称'].value_counts().head(10)
        for university, count in university_counts.items():
            print(f"{university}: {count} 个学科")
        
        print("\n" + "=" * 30)
        print("A+评估结果的学科分布:")
        print("=" * 30)
        a_plus = df[df['评估结果'] == 'A+']
        a_plus_subjects = a_plus['一级学科'].value_counts()
        for subject, count in a_plus_subjects.items():
            print(f"{subject}: {count} 个A+")
        
        print("\n" + "=" * 30)
        print("各地区高校数量:")
        print("=" * 30)
        region_counts = df['所在地区'].value_counts().head(15)
        for region, count in region_counts.items():
            print(f"{region}: {count} 条记录")
        
        # 保存一些统计信息到文件
        with open('data_analysis_summary.txt', 'w', encoding='utf-8') as f:
            f.write("学术桥教育部第四轮学科评估数据分析摘要\n")
            f.write("=" * 50 + "\n")
            f.write(f"总数据条数: {len(df)}\n")
            f.write(f"学科门类数量: {df['学科门类'].nunique()}\n")
            f.write(f"一级学科数量: {df['一级学科'].nunique()}\n")
            f.write(f"高校数量: {df['单位名称'].nunique()}\n\n")
            
            f.write("学科门类分布:\n")
            for category, count in category_counts.items():
                f.write(f"{category}: {count} 条\n")
            
            f.write("\n评估结果分布:\n")
            for result, count in result_counts.items():
                f.write(f"{result}: {count} 条\n")
        
        print(f"\n数据分析完成！详细信息已保存到 data_analysis_summary.txt")
        
    except Exception as e:
        print(f"分析数据时出错: {str(e)}")

if __name__ == "__main__":
    analyze_data()
