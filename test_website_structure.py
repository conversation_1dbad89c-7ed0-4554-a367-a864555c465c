#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试学术桥网站结构
分析页面元素和数据加载方式
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from bs4 import BeautifulSoup

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_website_structure():
    """测试网站结构"""
    chrome_options = Options()
    # 不使用无头模式，方便观察
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--window-size=1920,1080')
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        url = "https://www.acabridge.cn/acabridge/aca_web/xkpg4/index.html"
        logger.info(f"访问网站: {url}")
        driver.get(url)
        
        # 等待页面加载
        time.sleep(10)
        
        # 获取页面源码
        soup = BeautifulSoup(driver.page_source, 'html.parser')
        
        # 查找学科选择相关元素
        logger.info("=== 查找学科选择元素 ===")
        
        # 查找所有select元素
        selects = soup.find_all('select')
        logger.info(f"找到 {len(selects)} 个select元素")
        for i, select in enumerate(selects):
            logger.info(f"Select {i}: id={select.get('id')}, class={select.get('class')}")
            options = select.find_all('option')
            logger.info(f"  包含 {len(options)} 个选项")
            for j, option in enumerate(options[:5]):  # 只显示前5个选项
                logger.info(f"    选项 {j}: value={option.get('value')}, text={option.get_text(strip=True)}")
        
        # 查找表格
        logger.info("\n=== 查找数据表格 ===")
        tables = soup.find_all('table')
        logger.info(f"找到 {len(tables)} 个table元素")
        
        for i, table in enumerate(tables):
            logger.info(f"Table {i}: class={table.get('class')}, id={table.get('id')}")
            rows = table.find_all('tr')
            logger.info(f"  包含 {len(rows)} 行")
            
            if rows:
                # 显示表头
                header_row = rows[0]
                headers = header_row.find_all(['th', 'td'])
                header_texts = [h.get_text(strip=True) for h in headers]
                logger.info(f"  表头: {header_texts}")
                
                # 显示第一行数据（如果有）
                if len(rows) > 1:
                    data_row = rows[1]
                    data_cells = data_row.find_all('td')
                    data_texts = [cell.get_text(strip=True) for cell in data_cells]
                    logger.info(f"  第一行数据: {data_texts}")
        
        # 查找分页元素
        logger.info("\n=== 查找分页元素 ===")
        pagination_elements = soup.find_all(['a', 'button'], string=lambda text: text and ('下一页' in text or 'next' in text.lower()))
        logger.info(f"找到 {len(pagination_elements)} 个分页相关元素")
        for elem in pagination_elements:
            logger.info(f"  分页元素: tag={elem.name}, class={elem.get('class')}, text={elem.get_text(strip=True)}")
        
        # 查找所有可能的筛选元素
        logger.info("\n=== 查找筛选相关元素 ===")
        filter_elements = soup.find_all(['div', 'ul', 'li'], class_=lambda x: x and any(keyword in str(x).lower() for keyword in ['filter', 'category', 'subject', 'discipline']))
        logger.info(f"找到 {len(filter_elements)} 个筛选相关元素")
        for elem in filter_elements[:10]:  # 只显示前10个
            logger.info(f"  筛选元素: tag={elem.name}, class={elem.get('class')}, id={elem.get('id')}")
        
        # 保存完整的HTML到文件以便进一步分析
        with open('website_source.html', 'w', encoding='utf-8') as f:
            f.write(driver.page_source)
        logger.info("页面源码已保存到 website_source.html")
        
        # 尝试点击一些元素看看是否有动态加载
        logger.info("\n=== 测试动态交互 ===")
        
        # 查找可能的学科筛选按钮或链接
        subject_links = driver.find_elements(By.XPATH, "//a[contains(text(), '经济学') or contains(text(), '哲学') or contains(text(), '法学')]")
        logger.info(f"找到 {len(subject_links)} 个学科相关链接")
        
        if subject_links:
            logger.info("尝试点击第一个学科链接")
            subject_links[0].click()
            time.sleep(5)
            
            # 重新获取页面内容看是否有变化
            new_soup = BeautifulSoup(driver.page_source, 'html.parser')
            new_tables = new_soup.find_all('table')
            logger.info(f"点击后找到 {len(new_tables)} 个table元素")
        
    except Exception as e:
        logger.error(f"测试过程中出现异常: {str(e)}")
    
    finally:
        driver.quit()

if __name__ == "__main__":
    test_website_structure()
