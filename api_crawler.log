2025-07-03 16:24:03,313 - INFO - 开始爬取教育部第四轮学科评估数据
2025-07-03 16:24:03,314 - INFO - 请求页面: https://www.cingta.com/opendata/detail/191, 参数: {'name': '教育部第四轮学科评估数据'}
2025-07-03 16:24:03,639 - INFO - 开始爬取分类: 哲学 (01)
2025-07-03 16:24:03,639 - INFO - 尝试API: https://www.cingta.com/api/opendata/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '01', 'subject': '01', 'filter': '01'}
2025-07-03 16:24:03,701 - INFO - 尝试API: https://www.cingta.com/api/v1/opendata/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '01', 'subject': '01', 'filter': '01'}
2025-07-03 16:24:03,745 - INFO - 尝试API: https://www.cingta.com/opendata/api/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '01', 'subject': '01', 'filter': '01'}
2025-07-03 16:24:03,811 - INFO - 尝试API: https://www.cingta.com/api/opendata/subject-evaluation, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '01', 'subject': '01', 'filter': '01'}
2025-07-03 16:24:03,856 - INFO - 尝试API: https://www.cingta.com/api/opendata/data, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '01', 'subject': '01', 'filter': '01'}
2025-07-03 16:24:03,899 - INFO - 请求页面: https://www.cingta.com/opendata/detail/191, 参数: {'name': '教育部第四轮学科评估数据', 'category': '哲学'}
2025-07-03 16:24:03,971 - WARNING - 分类 哲学 未获取到数据
2025-07-03 16:24:05,972 - INFO - 开始爬取分类: 经济学 (02)
2025-07-03 16:24:05,973 - INFO - 尝试API: https://www.cingta.com/api/opendata/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '02', 'subject': '02', 'filter': '02'}
2025-07-03 16:24:06,020 - INFO - 尝试API: https://www.cingta.com/api/v1/opendata/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '02', 'subject': '02', 'filter': '02'}
2025-07-03 16:24:06,064 - INFO - 尝试API: https://www.cingta.com/opendata/api/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '02', 'subject': '02', 'filter': '02'}
2025-07-03 16:24:06,128 - INFO - 尝试API: https://www.cingta.com/api/opendata/subject-evaluation, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '02', 'subject': '02', 'filter': '02'}
2025-07-03 16:24:06,172 - INFO - 尝试API: https://www.cingta.com/api/opendata/data, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '02', 'subject': '02', 'filter': '02'}
2025-07-03 16:24:06,218 - INFO - 请求页面: https://www.cingta.com/opendata/detail/191, 参数: {'name': '教育部第四轮学科评估数据', 'category': '经济学'}
2025-07-03 16:24:06,286 - WARNING - 分类 经济学 未获取到数据
2025-07-03 16:24:08,287 - INFO - 开始爬取分类: 法学 (03)
2025-07-03 16:24:08,288 - INFO - 尝试API: https://www.cingta.com/api/opendata/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '03', 'subject': '03', 'filter': '03'}
2025-07-03 16:24:08,330 - INFO - 尝试API: https://www.cingta.com/api/v1/opendata/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '03', 'subject': '03', 'filter': '03'}
2025-07-03 16:24:08,374 - INFO - 尝试API: https://www.cingta.com/opendata/api/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '03', 'subject': '03', 'filter': '03'}
2025-07-03 16:24:08,435 - INFO - 尝试API: https://www.cingta.com/api/opendata/subject-evaluation, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '03', 'subject': '03', 'filter': '03'}
2025-07-03 16:24:08,491 - INFO - 尝试API: https://www.cingta.com/api/opendata/data, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '03', 'subject': '03', 'filter': '03'}
2025-07-03 16:24:08,534 - INFO - 请求页面: https://www.cingta.com/opendata/detail/191, 参数: {'name': '教育部第四轮学科评估数据', 'category': '法学'}
2025-07-03 16:24:08,601 - WARNING - 分类 法学 未获取到数据
2025-07-03 16:24:10,602 - INFO - 开始爬取分类: 教育学 (04)
2025-07-03 16:24:10,603 - INFO - 尝试API: https://www.cingta.com/api/opendata/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '04', 'subject': '04', 'filter': '04'}
2025-07-03 16:24:10,646 - INFO - 尝试API: https://www.cingta.com/api/v1/opendata/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '04', 'subject': '04', 'filter': '04'}
2025-07-03 16:24:10,690 - INFO - 尝试API: https://www.cingta.com/opendata/api/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '04', 'subject': '04', 'filter': '04'}
2025-07-03 16:24:10,750 - INFO - 尝试API: https://www.cingta.com/api/opendata/subject-evaluation, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '04', 'subject': '04', 'filter': '04'}
2025-07-03 16:24:10,795 - INFO - 尝试API: https://www.cingta.com/api/opendata/data, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '04', 'subject': '04', 'filter': '04'}
2025-07-03 16:24:10,840 - INFO - 请求页面: https://www.cingta.com/opendata/detail/191, 参数: {'name': '教育部第四轮学科评估数据', 'category': '教育学'}
2025-07-03 16:24:10,907 - WARNING - 分类 教育学 未获取到数据
2025-07-03 16:24:12,908 - INFO - 开始爬取分类: 文学 (05)
2025-07-03 16:24:12,909 - INFO - 尝试API: https://www.cingta.com/api/opendata/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '05', 'subject': '05', 'filter': '05'}
2025-07-03 16:24:12,953 - INFO - 尝试API: https://www.cingta.com/api/v1/opendata/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '05', 'subject': '05', 'filter': '05'}
2025-07-03 16:24:12,995 - INFO - 尝试API: https://www.cingta.com/opendata/api/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '05', 'subject': '05', 'filter': '05'}
2025-07-03 16:24:13,058 - INFO - 尝试API: https://www.cingta.com/api/opendata/subject-evaluation, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '05', 'subject': '05', 'filter': '05'}
2025-07-03 16:24:13,100 - INFO - 尝试API: https://www.cingta.com/api/opendata/data, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '05', 'subject': '05', 'filter': '05'}
2025-07-03 16:24:13,145 - INFO - 请求页面: https://www.cingta.com/opendata/detail/191, 参数: {'name': '教育部第四轮学科评估数据', 'category': '文学'}
2025-07-03 16:24:13,210 - WARNING - 分类 文学 未获取到数据
2025-07-03 16:24:15,211 - INFO - 开始爬取分类: 历史学 (06)
2025-07-03 16:24:15,211 - INFO - 尝试API: https://www.cingta.com/api/opendata/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '06', 'subject': '06', 'filter': '06'}
2025-07-03 16:24:15,256 - INFO - 尝试API: https://www.cingta.com/api/v1/opendata/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '06', 'subject': '06', 'filter': '06'}
2025-07-03 16:24:15,302 - INFO - 尝试API: https://www.cingta.com/opendata/api/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '06', 'subject': '06', 'filter': '06'}
2025-07-03 16:24:15,368 - INFO - 尝试API: https://www.cingta.com/api/opendata/subject-evaluation, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '06', 'subject': '06', 'filter': '06'}
2025-07-03 16:24:15,410 - INFO - 尝试API: https://www.cingta.com/api/opendata/data, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '06', 'subject': '06', 'filter': '06'}
2025-07-03 16:24:15,453 - INFO - 请求页面: https://www.cingta.com/opendata/detail/191, 参数: {'name': '教育部第四轮学科评估数据', 'category': '历史学'}
2025-07-03 16:24:15,523 - WARNING - 分类 历史学 未获取到数据
2025-07-03 16:24:17,523 - INFO - 开始爬取分类: 理学 (07)
2025-07-03 16:24:17,524 - INFO - 尝试API: https://www.cingta.com/api/opendata/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '07', 'subject': '07', 'filter': '07'}
2025-07-03 16:24:17,566 - INFO - 尝试API: https://www.cingta.com/api/v1/opendata/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '07', 'subject': '07', 'filter': '07'}
2025-07-03 16:24:17,610 - INFO - 尝试API: https://www.cingta.com/opendata/api/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '07', 'subject': '07', 'filter': '07'}
2025-07-03 16:24:17,671 - INFO - 尝试API: https://www.cingta.com/api/opendata/subject-evaluation, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '07', 'subject': '07', 'filter': '07'}
2025-07-03 16:24:17,715 - INFO - 尝试API: https://www.cingta.com/api/opendata/data, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '07', 'subject': '07', 'filter': '07'}
2025-07-03 16:24:17,756 - INFO - 请求页面: https://www.cingta.com/opendata/detail/191, 参数: {'name': '教育部第四轮学科评估数据', 'category': '理学'}
2025-07-03 16:24:17,826 - WARNING - 分类 理学 未获取到数据
2025-07-03 16:24:19,827 - INFO - 开始爬取分类: 工学 (08)
2025-07-03 16:24:19,828 - INFO - 尝试API: https://www.cingta.com/api/opendata/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '08', 'subject': '08', 'filter': '08'}
2025-07-03 16:24:19,871 - INFO - 尝试API: https://www.cingta.com/api/v1/opendata/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '08', 'subject': '08', 'filter': '08'}
2025-07-03 16:24:19,916 - INFO - 尝试API: https://www.cingta.com/opendata/api/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '08', 'subject': '08', 'filter': '08'}
2025-07-03 16:24:19,995 - INFO - 尝试API: https://www.cingta.com/api/opendata/subject-evaluation, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '08', 'subject': '08', 'filter': '08'}
2025-07-03 16:24:20,043 - INFO - 尝试API: https://www.cingta.com/api/opendata/data, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '08', 'subject': '08', 'filter': '08'}
2025-07-03 16:24:20,089 - INFO - 请求页面: https://www.cingta.com/opendata/detail/191, 参数: {'name': '教育部第四轮学科评估数据', 'category': '工学'}
2025-07-03 16:24:20,157 - WARNING - 分类 工学 未获取到数据
2025-07-03 16:24:22,158 - INFO - 开始爬取分类: 农学 (09)
2025-07-03 16:24:22,159 - INFO - 尝试API: https://www.cingta.com/api/opendata/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '09', 'subject': '09', 'filter': '09'}
2025-07-03 16:24:22,200 - INFO - 尝试API: https://www.cingta.com/api/v1/opendata/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '09', 'subject': '09', 'filter': '09'}
2025-07-03 16:24:22,242 - INFO - 尝试API: https://www.cingta.com/opendata/api/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '09', 'subject': '09', 'filter': '09'}
2025-07-03 16:24:22,307 - INFO - 尝试API: https://www.cingta.com/api/opendata/subject-evaluation, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '09', 'subject': '09', 'filter': '09'}
2025-07-03 16:24:22,350 - INFO - 尝试API: https://www.cingta.com/api/opendata/data, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '09', 'subject': '09', 'filter': '09'}
2025-07-03 16:24:22,393 - INFO - 请求页面: https://www.cingta.com/opendata/detail/191, 参数: {'name': '教育部第四轮学科评估数据', 'category': '农学'}
2025-07-03 16:24:22,458 - WARNING - 分类 农学 未获取到数据
2025-07-03 16:24:24,459 - INFO - 开始爬取分类: 医学 (10)
2025-07-03 16:24:24,460 - INFO - 尝试API: https://www.cingta.com/api/opendata/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '10', 'subject': '10', 'filter': '10'}
2025-07-03 16:24:24,504 - INFO - 尝试API: https://www.cingta.com/api/v1/opendata/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '10', 'subject': '10', 'filter': '10'}
2025-07-03 16:24:24,548 - INFO - 尝试API: https://www.cingta.com/opendata/api/list, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '10', 'subject': '10', 'filter': '10'}
2025-07-03 16:24:24,605 - INFO - 尝试API: https://www.cingta.com/api/opendata/subject-evaluation, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '10', 'subject': '10', 'filter': '10'}
2025-07-03 16:24:24,648 - INFO - 尝试API: https://www.cingta.com/api/opendata/data, 参数: {'datasetId': 191, 'page': 1, 'pageSize': 100, 'category': '10', 'subject': '10', 'filter': '10'}
2025-07-03 16:24:24,691 - INFO - 请求页面: https://www.cingta.com/opendata/detail/191, 参数: {'name': '教育部第四轮学科评估数据', 'category': '医学'}
2025-07-03 16:24:24,759 - WARNING - 分类 医学 未获取到数据
2025-07-03 16:24:26,487 - INFO - 用户中断爬取
