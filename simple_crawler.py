#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
教育部第四轮学科评估数据爬虫 - 简化版本
通过分析网页结构直接获取数据
"""

import requests
import json
import time
import csv
import re
from typing import List, Dict, Any
import logging
from bs4 import BeautifulSoup

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SimpleCrawler:
    def __init__(self):
        self.base_url = "https://www.cingta.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # 学科分类列表
        self.subject_categories = [
            '哲学', '经济学', '法学', '教育学', '文学', '历史学',
            '理学', '工学', '农学', '医学', '管理学', '艺术学', '交叉学科'
        ]
        
        self.all_data = []
        
    def get_page_content(self, url: str) -> str:
        """
        获取页面内容
        """
        try:
            logger.info(f"请求URL: {url}")
            response = self.session.get(url, timeout=30)
            
            if response.status_code == 200:
                return response.text
            else:
                logger.error(f"请求失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"请求异常: {str(e)}")
            return None
    
    def extract_data_from_script(self, html_content: str) -> List[Dict[str, Any]]:
        """
        从页面的JavaScript代码中提取数据
        """
        try:
            # 查找包含数据的script标签
            soup = BeautifulSoup(html_content, 'html.parser')
            scripts = soup.find_all('script')
            
            for script in scripts:
                if script.string and ('data' in script.string or 'list' in script.string):
                    script_content = script.string
                    
                    # 尝试提取JSON数据
                    json_matches = re.findall(r'(\[.*?\])', script_content, re.DOTALL)
                    for match in json_matches:
                        try:
                            data = json.loads(match)
                            if isinstance(data, list) and len(data) > 0:
                                logger.info(f"从script中提取到 {len(data)} 条数据")
                                return data
                        except:
                            continue
                            
            return []
            
        except Exception as e:
            logger.error(f"提取script数据异常: {str(e)}")
            return []
    
    def parse_table_from_html(self, html_content: str, category: str) -> List[Dict[str, Any]]:
        """
        从HTML中解析表格数据
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找表格
            tables = soup.find_all('table')
            data_list = []
            
            for table in tables:
                rows = table.find_all('tr')
                
                # 检查是否是数据表格（包含序号、单位名称等列）
                if len(rows) > 1:
                    header_row = rows[0]
                    headers = [th.get_text(strip=True) for th in header_row.find_all(['th', 'td'])]
                    
                    # 检查表头是否包含预期的列
                    if any(keyword in ''.join(headers) for keyword in ['序号', '单位', '学科', '评估']):
                        logger.info(f"找到数据表格，表头: {headers}")
                        
                        for i, row in enumerate(rows[1:], 1):
                            cells = row.find_all('td')
                            if len(cells) >= 5:
                                data_item = {
                                    '序号': cells[0].get_text(strip=True),
                                    '单位名称': cells[1].get_text(strip=True),
                                    '一级学科': cells[2].get_text(strip=True),
                                    '评估结果': cells[3].get_text(strip=True),
                                    '排名占比': cells[4].get_text(strip=True),
                                    '学科分类': category
                                }
                                data_list.append(data_item)
                                
            logger.info(f"从HTML表格解析到 {len(data_list)} 条数据")
            return data_list
            
        except Exception as e:
            logger.error(f"解析HTML表格异常: {str(e)}")
            return []
    
    def crawl_category_data(self, category: str) -> List[Dict[str, Any]]:
        """
        爬取指定分类的数据
        """
        logger.info(f"开始爬取分类: {category}")
        category_data = []
        
        try:
            # 构建URL - 尝试不同的参数组合
            urls_to_try = [
                f"{self.base_url}/opendata/detail/191?name=教育部第四轮学科评估数据&category={category}",
                f"{self.base_url}/opendata/detail/191?category={category}",
                f"{self.base_url}/opendata/detail/191?subject={category}",
            ]
            
            for url in urls_to_try:
                html_content = self.get_page_content(url)
                if html_content:
                    # 尝试从script中提取数据
                    script_data = self.extract_data_from_script(html_content)
                    if script_data:
                        # 处理script数据
                        for item in script_data:
                            if isinstance(item, dict):
                                item['学科分类'] = category
                        category_data.extend(script_data)
                        break
                    
                    # 尝试从HTML表格中提取数据
                    table_data = self.parse_table_from_html(html_content, category)
                    if table_data:
                        category_data.extend(table_data)
                        break
                        
                time.sleep(2)  # 避免请求过快
                
        except Exception as e:
            logger.error(f"爬取分类 {category} 异常: {str(e)}")
            
        logger.info(f"分类 {category} 获取到 {len(category_data)} 条数据")
        return category_data
    
    def crawl_all_data(self):
        """
        爬取所有分类的数据
        """
        logger.info("开始爬取所有学科分类数据")
        
        # 首先尝试获取主页面的所有数据
        main_url = f"{self.base_url}/opendata/detail/191?name=教育部第四轮学科评估数据"
        html_content = self.get_page_content(main_url)
        
        if html_content:
            # 尝试从主页面获取所有数据
            all_data = self.parse_table_from_html(html_content, "全部")
            if all_data:
                self.all_data = all_data
                logger.info(f"从主页面获取到所有数据: {len(all_data)} 条")
                return
        
        # 如果主页面没有获取到数据，则分类爬取
        for category in self.subject_categories:
            try:
                category_data = self.crawl_category_data(category)
                self.all_data.extend(category_data)
                
                logger.info(f"已完成分类 {category}，累计数据: {len(self.all_data)} 条")
                time.sleep(3)  # 分类间休息
                
            except Exception as e:
                logger.error(f"爬取分类 {category} 失败: {str(e)}")
                continue
                
        logger.info(f"所有分类爬取完成，总计: {len(self.all_data)} 条数据")
    
    def save_to_csv(self, filename: str = "subject_evaluation_simple.csv"):
        """
        保存数据到CSV文件
        """
        if not self.all_data:
            logger.warning("没有数据可保存")
            return
            
        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                if self.all_data:
                    fieldnames = list(self.all_data[0].keys())
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    
                    writer.writeheader()
                    for item in self.all_data:
                        writer.writerow(item)
                        
            logger.info(f"数据已保存到 {filename}，共 {len(self.all_data)} 条记录")
            
        except Exception as e:
            logger.error(f"保存CSV文件失败: {str(e)}")
    
    def save_to_json(self, filename: str = "subject_evaluation_simple.json"):
        """
        保存数据到JSON文件
        """
        if not self.all_data:
            logger.warning("没有数据可保存")
            return
            
        try:
            with open(filename, 'w', encoding='utf-8') as jsonfile:
                json.dump(self.all_data, jsonfile, ensure_ascii=False, indent=2)
                
            logger.info(f"数据已保存到 {filename}，共 {len(self.all_data)} 条记录")
            
        except Exception as e:
            logger.error(f"保存JSON文件失败: {str(e)}")

def main():
    """
    主函数
    """
    crawler = SimpleCrawler()
    
    try:
        # 爬取所有数据
        crawler.crawl_all_data()
        
        # 保存数据
        crawler.save_to_csv()
        crawler.save_to_json()
        
        print(f"爬取完成！共获取 {len(crawler.all_data)} 条数据")
        
    except KeyboardInterrupt:
        logger.info("用户中断爬取")
        print("爬取被中断")
        
    except Exception as e:
        logger.error(f"爬取过程发生异常: {str(e)}")
        print(f"爬取失败: {str(e)}")

if __name__ == "__main__":
    main()
