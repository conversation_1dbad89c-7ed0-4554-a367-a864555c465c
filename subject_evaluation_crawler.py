#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
教育部第四轮学科评估数据爬虫
爬取所有学科分类的评估数据
使用Selenium处理JavaScript渲染的页面
"""

import requests
import json
import time
import csv
import os
from typing import List, Dict, Any
import logging
from urllib.parse import urlencode
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from bs4 import BeautifulSoup

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SubjectEvaluationCrawler:
    def __init__(self):
        self.base_url = "https://www.cingta.com"
        self.driver = None

        # 学科分类列表
        self.subject_categories = [
            '哲学', '经济学', '法学', '教育学', '文学', '历史学',
            '理学', '工学', '农学', '医学', '管理学', '艺术学', '交叉学科'
        ]

        self.all_data = []

    def setup_driver(self):
        """
        设置Chrome浏览器驱动
        """
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # 无头模式
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')

            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            logger.info("Chrome驱动初始化成功")

        except Exception as e:
            logger.error(f"Chrome驱动初始化失败: {str(e)}")
            raise

    def close_driver(self):
        """
        关闭浏览器驱动
        """
        if self.driver:
            self.driver.quit()
            logger.info("浏览器驱动已关闭")
        
    def click_category(self, category_name: str) -> bool:
        """
        点击指定的学科分类
        """
        try:
            # 等待页面加载完成
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.CLASS_NAME, "opendata-filter-item"))
            )

            # 查找并点击指定分类
            category_elements = self.driver.find_elements(By.CLASS_NAME, "opendata-filter-item")

            for element in category_elements:
                if category_name in element.text:
                    logger.info(f"找到分类: {category_name}")
                    element.click()
                    time.sleep(3)  # 等待数据加载
                    return True

            logger.warning(f"未找到分类: {category_name}")
            return False

        except Exception as e:
            logger.error(f"点击分类 {category_name} 失败: {str(e)}")
            return False
    
    def parse_table_data(self, category: str) -> List[Dict[str, Any]]:
        """
        解析当前页面的表格数据
        """
        try:
            # 等待表格加载
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.TAG_NAME, "table"))
            )

            # 获取页面源码并解析
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')

            # 查找数据表格
            table = soup.find('table')
            if not table:
                logger.warning("未找到数据表格")
                return []

            rows = table.find_all('tr')
            data_list = []

            # 跳过表头，从第二行开始解析
            for i, row in enumerate(rows[1:], 1):
                cells = row.find_all('td')
                if len(cells) >= 5:
                    data_item = {
                        '序号': cells[0].get_text(strip=True),
                        '单位名称': cells[1].get_text(strip=True),
                        '一级学科': cells[2].get_text(strip=True),
                        '评估结果': cells[3].get_text(strip=True),
                        '排名占比': cells[4].get_text(strip=True),
                        '学科分类': category
                    }
                    data_list.append(data_item)

            logger.info(f"解析到 {len(data_list)} 条数据")
            return data_list

        except Exception as e:
            logger.error(f"解析表格数据异常: {str(e)}")
            return []
    
    def get_all_pages_data(self, category: str) -> List[Dict[str, Any]]:
        """
        获取指定分类的所有页面数据
        """
        category_data = []
        page = 1

        while True:
            try:
                logger.info(f"正在获取分类 {category} 第 {page} 页数据")

                # 解析当前页面数据
                page_data = self.parse_table_data(category)

                if not page_data:
                    logger.info(f"分类 {category} 第 {page} 页无数据，结束爬取")
                    break

                category_data.extend(page_data)
                logger.info(f"分类 {category} 第 {page} 页获取到 {len(page_data)} 条数据")

                # 尝试点击下一页
                if not self.go_to_next_page():
                    logger.info(f"分类 {category} 已到最后一页")
                    break

                page += 1
                time.sleep(2)  # 等待页面加载

            except Exception as e:
                logger.error(f"获取分类 {category} 第 {page} 页数据异常: {str(e)}")
                break

        return category_data

    def go_to_next_page(self) -> bool:
        """
        尝试翻到下一页
        """
        try:
            # 查找下一页按钮
            next_buttons = self.driver.find_elements(By.XPATH, "//a[contains(text(), '下一页') or contains(@class, 'next')]")

            for button in next_buttons:
                if button.is_enabled() and button.is_displayed():
                    button.click()
                    time.sleep(3)  # 等待页面加载
                    return True

            # 如果没有找到"下一页"按钮，尝试查找数字页码
            page_links = self.driver.find_elements(By.XPATH, "//a[contains(@class, 'page-link') or contains(@class, 'pagination')]")

            for link in page_links:
                if link.text.isdigit() and link.is_enabled():
                    link.click()
                    time.sleep(3)
                    return True

            return False

        except Exception as e:
            logger.error(f"翻页失败: {str(e)}")
            return False
    
    def crawl_all_categories(self):
        """
        爬取所有分类的数据
        """
        logger.info("开始爬取所有学科分类数据")

        try:
            # 初始化浏览器
            self.setup_driver()

            # 访问主页面
            main_url = f"{self.base_url}/opendata/detail/191?name=教育部第四轮学科评估数据"
            logger.info(f"访问主页面: {main_url}")
            self.driver.get(main_url)

            # 等待页面加载
            time.sleep(5)

            for category_name in self.subject_categories:
                try:
                    logger.info(f"开始爬取分类: {category_name}")

                    # 点击分类
                    if self.click_category(category_name):
                        # 获取该分类的所有数据
                        category_data = self.get_all_pages_data(category_name)
                        self.all_data.extend(category_data)

                        logger.info(f"分类 {category_name} 完成，获取 {len(category_data)} 条数据")
                        logger.info(f"累计数据: {len(self.all_data)} 条")
                    else:
                        logger.warning(f"无法点击分类 {category_name}")

                    # 分类间休息
                    time.sleep(3)

                except Exception as e:
                    logger.error(f"爬取分类 {category_name} 失败: {str(e)}")
                    continue

        except Exception as e:
            logger.error(f"爬取过程异常: {str(e)}")
        finally:
            self.close_driver()

        logger.info(f"所有分类爬取完成，总计: {len(self.all_data)} 条数据")
    
    def save_to_csv(self, filename: str = "subject_evaluation_data.csv"):
        """
        保存数据到CSV文件
        """
        if not self.all_data:
            logger.warning("没有数据可保存")
            return
            
        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = ['序号', '单位名称', '一级学科', '评估结果', '排名占比', '学科分类']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for item in self.all_data:
                    writer.writerow(item)
                    
            logger.info(f"数据已保存到 {filename}，共 {len(self.all_data)} 条记录")
            
        except Exception as e:
            logger.error(f"保存CSV文件失败: {str(e)}")
    
    def save_to_json(self, filename: str = "subject_evaluation_data.json"):
        """
        保存数据到JSON文件
        """
        if not self.all_data:
            logger.warning("没有数据可保存")
            return
            
        try:
            with open(filename, 'w', encoding='utf-8') as jsonfile:
                json.dump(self.all_data, jsonfile, ensure_ascii=False, indent=2)
                
            logger.info(f"数据已保存到 {filename}，共 {len(self.all_data)} 条记录")
            
        except Exception as e:
            logger.error(f"保存JSON文件失败: {str(e)}")

def main():
    """
    主函数
    """
    crawler = SubjectEvaluationCrawler()
    
    try:
        # 爬取所有分类数据
        crawler.crawl_all_categories()
        
        # 保存数据
        crawler.save_to_csv()
        crawler.save_to_json()
        
        print(f"爬取完成！共获取 {len(crawler.all_data)} 条数据")
        
    except KeyboardInterrupt:
        logger.info("用户中断爬取")
        print("爬取被中断")
        
    except Exception as e:
        logger.error(f"爬取过程发生异常: {str(e)}")
        print(f"爬取失败: {str(e)}")

if __name__ == "__main__":
    main()
