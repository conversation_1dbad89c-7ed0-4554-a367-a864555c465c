#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
教育部第四轮学科评估数据爬虫 - 最终版本
通过模拟真实浏览器行为获取数据
"""

import requests
import json
import time
import csv
import re
import logging
from typing import List, Dict, Any
from bs4 import BeautifulSoup

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('final_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FinalCrawler:
    def __init__(self):
        self.base_url = "https://www.cingta.com"
        self.session = requests.Session()
        
        # 模拟真实浏览器的请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        })
        
        # 学科分类列表
        self.subject_categories = [
            '哲学', '经济学', '法学', '教育学', '文学', '历史学',
            '理学', '工学', '农学', '医学', '管理学', '艺术学', '交叉学科'
        ]
        
        self.all_data = []
        
    def get_initial_page(self) -> str:
        """
        获取初始页面内容
        """
        try:
            url = f"{self.base_url}/opendata/detail/191"
            params = {'name': '教育部第四轮学科评估数据'}
            
            logger.info(f"获取初始页面: {url}")
            response = self.session.get(url, params=params, timeout=30)
            
            if response.status_code == 200:
                return response.text
            else:
                logger.error(f"获取页面失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"获取页面异常: {str(e)}")
            return None
    
    def extract_all_data_from_page(self, html_content: str) -> List[Dict[str, Any]]:
        """
        从页面中提取所有可能的数据
        """
        all_data = []
        
        try:
            # 方法1: 查找JavaScript中的数据
            js_data = self.extract_from_javascript(html_content)
            if js_data:
                all_data.extend(js_data)
                logger.info(f"从JavaScript提取到 {len(js_data)} 条数据")
            
            # 方法2: 解析HTML表格
            soup = BeautifulSoup(html_content, 'html.parser')
            table_data = self.extract_from_tables(soup)
            if table_data:
                all_data.extend(table_data)
                logger.info(f"从HTML表格提取到 {len(table_data)} 条数据")
            
            # 方法3: 查找隐藏的数据元素
            hidden_data = self.extract_from_hidden_elements(soup)
            if hidden_data:
                all_data.extend(hidden_data)
                logger.info(f"从隐藏元素提取到 {len(hidden_data)} 条数据")
            
            return all_data
            
        except Exception as e:
            logger.error(f"提取数据异常: {str(e)}")
            return []
    
    def extract_from_javascript(self, html_content: str) -> List[Dict[str, Any]]:
        """
        从JavaScript代码中提取数据
        """
        try:
            # 更全面的正则表达式模式
            patterns = [
                r'window\.__INITIAL_STATE__\s*=\s*({.*?});',
                r'window\.__DATA__\s*=\s*({.*?});',
                r'window\.pageData\s*=\s*({.*?});',
                r'var\s+pageData\s*=\s*({.*?});',
                r'const\s+pageData\s*=\s*({.*?});',
                r'let\s+pageData\s*=\s*({.*?});',
                r'data:\s*(\[.*?\])',
                r'"data":\s*(\[.*?\])',
                r'list:\s*(\[.*?\])',
                r'"list":\s*(\[.*?\])',
                r'tableData:\s*(\[.*?\])',
                r'"tableData":\s*(\[.*?\])',
                r'evaluationData:\s*(\[.*?\])',
                r'"evaluationData":\s*(\[.*?\])'
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, html_content, re.DOTALL | re.IGNORECASE)
                for match in matches:
                    try:
                        # 清理JSON字符串
                        clean_match = match.strip()
                        if clean_match.endswith(','):
                            clean_match = clean_match[:-1]
                        
                        data = json.loads(clean_match)
                        
                        if isinstance(data, list):
                            return self.process_data_list(data)
                        elif isinstance(data, dict):
                            # 查找字典中的数据列表
                            for key in ['data', 'list', 'items', 'records', 'tableData', 'evaluationData']:
                                if key in data and isinstance(data[key], list):
                                    return self.process_data_list(data[key])
                                    
                    except json.JSONDecodeError:
                        continue
                        
            return []
            
        except Exception as e:
            logger.error(f"从JavaScript提取数据异常: {str(e)}")
            return []
    
    def extract_from_tables(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """
        从HTML表格中提取数据
        """
        try:
            tables = soup.find_all('table')
            all_table_data = []
            
            for table in tables:
                rows = table.find_all('tr')
                
                if len(rows) <= 1:
                    continue
                
                # 获取表头
                header_row = rows[0]
                headers = []
                for cell in header_row.find_all(['th', 'td']):
                    headers.append(cell.get_text(strip=True))
                
                # 检查是否是目标表格
                header_text = ' '.join(headers).lower()
                if not any(keyword in header_text for keyword in ['序号', '单位', '学科', '评估', 'university', 'subject', 'grade']):
                    continue
                
                logger.info(f"找到目标表格，表头: {headers}")
                
                # 提取数据行
                for row in rows[1:]:
                    cells = row.find_all('td')
                    
                    if len(cells) >= 5:
                        data_item = {
                            '序号': cells[0].get_text(strip=True),
                            '单位名称': cells[1].get_text(strip=True),
                            '一级学科': cells[2].get_text(strip=True),
                            '评估结果': cells[3].get_text(strip=True),
                            '排名占比': cells[4].get_text(strip=True),
                            '学科分类': '未分类'
                        }
                        
                        # 过滤有效数据
                        if data_item['单位名称'] and data_item['一级学科']:
                            all_table_data.append(data_item)
            
            return all_table_data
            
        except Exception as e:
            logger.error(f"从表格提取数据异常: {str(e)}")
            return []
    
    def extract_from_hidden_elements(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """
        从隐藏元素中提取数据
        """
        try:
            # 查找可能包含数据的隐藏元素
            hidden_elements = soup.find_all(['script', 'div', 'span'], {'style': re.compile(r'display:\s*none', re.I)})
            hidden_elements.extend(soup.find_all(attrs={'class': re.compile(r'hidden', re.I)}))
            
            for element in hidden_elements:
                text = element.get_text()
                if text and len(text) > 100:  # 只处理较长的文本
                    try:
                        # 尝试解析为JSON
                        data = json.loads(text)
                        if isinstance(data, list):
                            return self.process_data_list(data)
                    except:
                        continue
            
            return []
            
        except Exception as e:
            logger.error(f"从隐藏元素提取数据异常: {str(e)}")
            return []
    
    def process_data_list(self, data_list: List[Any]) -> List[Dict[str, Any]]:
        """
        处理数据列表
        """
        processed_data = []
        
        for item in data_list:
            if isinstance(item, dict):
                # 尝试映射各种可能的字段名
                processed_item = {}
                
                # 序号
                for key in ['id', 'index', 'no', 'number', '序号']:
                    if key in item:
                        processed_item['序号'] = str(item[key])
                        break
                else:
                    processed_item['序号'] = ''
                
                # 单位名称
                for key in ['university', 'school', 'institution', 'name', '单位名称', '学校名称']:
                    if key in item:
                        processed_item['单位名称'] = str(item[key])
                        break
                else:
                    processed_item['单位名称'] = ''
                
                # 一级学科
                for key in ['subject', 'discipline', 'major', '一级学科', '学科']:
                    if key in item:
                        processed_item['一级学科'] = str(item[key])
                        break
                else:
                    processed_item['一级学科'] = ''
                
                # 评估结果
                for key in ['grade', 'result', 'score', 'rating', '评估结果', '等级']:
                    if key in item:
                        processed_item['评估结果'] = str(item[key])
                        break
                else:
                    processed_item['评估结果'] = ''
                
                # 排名占比
                for key in ['rank', 'percentage', 'ratio', '排名占比', '占比']:
                    if key in item:
                        processed_item['排名占比'] = str(item[key])
                        break
                else:
                    processed_item['排名占比'] = ''
                
                # 学科分类
                for key in ['category', 'type', 'classification', '学科分类', '分类']:
                    if key in item:
                        processed_item['学科分类'] = str(item[key])
                        break
                else:
                    processed_item['学科分类'] = '未分类'
                
                # 只保留有效数据
                if processed_item['单位名称'] and processed_item['一级学科']:
                    processed_data.append(processed_item)
        
        return processed_data
    
    def crawl_all_data(self):
        """
        爬取所有数据
        """
        logger.info("开始爬取教育部第四轮学科评估数据")
        
        try:
            # 获取初始页面
            html_content = self.get_initial_page()
            if not html_content:
                logger.error("无法获取页面内容")
                return
            
            # 从页面中提取所有数据
            all_data = self.extract_all_data_from_page(html_content)
            
            if all_data:
                self.all_data = all_data
                logger.info(f"成功提取数据: {len(all_data)} 条")
            else:
                logger.warning("未能从页面提取到数据")
                
                # 如果主页面没有数据，尝试分类获取
                for category in self.subject_categories:
                    try:
                        logger.info(f"尝试获取分类: {category}")
                        category_url = f"{self.base_url}/opendata/detail/191?name=教育部第四轮学科评估数据&category={category}"
                        
                        response = self.session.get(category_url, timeout=30)
                        if response.status_code == 200:
                            category_data = self.extract_all_data_from_page(response.text)
                            if category_data:
                                # 更新分类信息
                                for item in category_data:
                                    item['学科分类'] = category
                                
                                self.all_data.extend(category_data)
                                logger.info(f"分类 {category} 获取到 {len(category_data)} 条数据")
                        
                        time.sleep(2)  # 避免请求过快
                        
                    except Exception as e:
                        logger.error(f"获取分类 {category} 数据失败: {str(e)}")
                        continue
            
        except Exception as e:
            logger.error(f"爬取过程异常: {str(e)}")
        
        logger.info(f"爬取完成，总计: {len(self.all_data)} 条数据")
    
    def save_to_csv(self, filename: str = "subject_evaluation_final.csv"):
        """
        保存数据到CSV文件
        """
        if not self.all_data:
            logger.warning("没有数据可保存")
            return
            
        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = ['序号', '单位名称', '一级学科', '评估结果', '排名占比', '学科分类']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for item in self.all_data:
                    writer.writerow(item)
                    
            logger.info(f"数据已保存到 {filename}，共 {len(self.all_data)} 条记录")
            
        except Exception as e:
            logger.error(f"保存CSV文件失败: {str(e)}")
    
    def save_to_json(self, filename: str = "subject_evaluation_final.json"):
        """
        保存数据到JSON文件
        """
        if not self.all_data:
            logger.warning("没有数据可保存")
            return
            
        try:
            with open(filename, 'w', encoding='utf-8') as jsonfile:
                json.dump(self.all_data, jsonfile, ensure_ascii=False, indent=2)
                
            logger.info(f"数据已保存到 {filename}，共 {len(self.all_data)} 条记录")
            
        except Exception as e:
            logger.error(f"保存JSON文件失败: {str(e)}")

def main():
    """
    主函数
    """
    crawler = FinalCrawler()
    
    try:
        # 爬取所有数据
        crawler.crawl_all_data()
        
        # 保存数据
        crawler.save_to_csv()
        crawler.save_to_json()
        
        print(f"爬取完成！共获取 {len(crawler.all_data)} 条数据")
        
        if crawler.all_data:
            print("\n数据示例:")
            for i, item in enumerate(crawler.all_data[:3]):
                print(f"{i+1}. {item}")
        
    except KeyboardInterrupt:
        logger.info("用户中断爬取")
        print("爬取被中断")
        
    except Exception as e:
        logger.error(f"爬取过程发生异常: {str(e)}")
        print(f"爬取失败: {str(e)}")

if __name__ == "__main__":
    main()
