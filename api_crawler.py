#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
教育部第四轮学科评估数据爬虫 - API版本
通过分析网络请求直接调用API获取数据
"""

import requests
import json
import time
import csv
import logging
from typing import List, Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('api_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class APICrawler:
    def __init__(self):
        self.base_url = "https://www.cingta.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://www.cingta.com/opendata/detail/191',
            'Origin': 'https://www.cingta.com'
        })
        
        # 学科分类映射
        self.subject_categories = {
            '哲学': '01',
            '经济学': '02', 
            '法学': '03',
            '教育学': '04',
            '文学': '05',
            '历史学': '06',
            '理学': '07',
            '工学': '08',
            '农学': '09',
            '医学': '10',
            '管理学': '12',
            '艺术学': '13',
            '交叉学科': '14'
        }
        
        self.all_data = []
        
    def get_api_data(self, category_code: str = None, page: int = 1, page_size: int = 100) -> Dict[str, Any]:
        """
        调用API获取数据
        """
        try:
            # 尝试不同的API端点
            api_endpoints = [
                f"{self.base_url}/api/opendata/list",
                f"{self.base_url}/api/v1/opendata/list", 
                f"{self.base_url}/opendata/api/list",
                f"{self.base_url}/api/opendata/subject-evaluation",
                f"{self.base_url}/api/opendata/data"
            ]
            
            for api_url in api_endpoints:
                try:
                    params = {
                        'datasetId': 191,
                        'page': page,
                        'pageSize': page_size
                    }
                    
                    if category_code:
                        params['category'] = category_code
                        params['subject'] = category_code
                        params['filter'] = category_code
                    
                    logger.info(f"尝试API: {api_url}, 参数: {params}")
                    
                    response = self.session.get(api_url, params=params, timeout=30)
                    
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            if data and isinstance(data, dict):
                                logger.info(f"API调用成功: {api_url}")
                                return data
                        except json.JSONDecodeError:
                            continue
                    
                except Exception as e:
                    logger.debug(f"API {api_url} 调用失败: {str(e)}")
                    continue
                    
            return None
            
        except Exception as e:
            logger.error(f"API调用异常: {str(e)}")
            return None
    
    def get_page_data_direct(self, category: str = None) -> List[Dict[str, Any]]:
        """
        直接从页面获取数据
        """
        try:
            url = f"{self.base_url}/opendata/detail/191"
            params = {'name': '教育部第四轮学科评估数据'}
            
            if category:
                params['category'] = category
            
            logger.info(f"请求页面: {url}, 参数: {params}")
            
            response = self.session.get(url, params=params, timeout=30)
            
            if response.status_code == 200:
                # 尝试从页面源码中提取数据
                html_content = response.text
                
                # 查找JSON数据
                import re
                
                # 查找可能包含数据的JSON字符串
                json_patterns = [
                    r'window\.__INITIAL_STATE__\s*=\s*({.*?});',
                    r'window\.__DATA__\s*=\s*({.*?});',
                    r'var\s+data\s*=\s*(\[.*?\]);',
                    r'data:\s*(\[.*?\])',
                    r'"data":\s*(\[.*?\])',
                    r'list:\s*(\[.*?\])',
                    r'"list":\s*(\[.*?\])'
                ]
                
                for pattern in json_patterns:
                    matches = re.findall(pattern, html_content, re.DOTALL)
                    for match in matches:
                        try:
                            data = json.loads(match)
                            if isinstance(data, list) and len(data) > 0:
                                logger.info(f"从页面提取到数据: {len(data)} 条")
                                return self.process_raw_data(data, category)
                            elif isinstance(data, dict) and 'data' in data:
                                if isinstance(data['data'], list):
                                    logger.info(f"从页面提取到数据: {len(data['data'])} 条")
                                    return self.process_raw_data(data['data'], category)
                        except json.JSONDecodeError:
                            continue
                
                # 如果没有找到JSON数据，尝试解析HTML表格
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(html_content, 'html.parser')
                return self.parse_html_table(soup, category)
                
            return []
            
        except Exception as e:
            logger.error(f"获取页面数据异常: {str(e)}")
            return []
    
    def process_raw_data(self, raw_data: List[Any], category: str) -> List[Dict[str, Any]]:
        """
        处理原始数据
        """
        processed_data = []
        
        for item in raw_data:
            if isinstance(item, dict):
                # 尝试映射字段
                processed_item = {
                    '序号': item.get('id', item.get('序号', '')),
                    '单位名称': item.get('university', item.get('单位名称', item.get('school', ''))),
                    '一级学科': item.get('subject', item.get('一级学科', item.get('discipline', ''))),
                    '评估结果': item.get('grade', item.get('评估结果', item.get('result', ''))),
                    '排名占比': item.get('rank', item.get('排名占比', item.get('percentage', ''))),
                    '学科分类': category or item.get('category', item.get('学科分类', ''))
                }
                
                # 过滤有效数据
                if processed_item['单位名称'] and processed_item['一级学科']:
                    processed_data.append(processed_item)
        
        return processed_data
    
    def parse_html_table(self, soup, category: str) -> List[Dict[str, Any]]:
        """
        解析HTML表格
        """
        try:
            tables = soup.find_all('table')
            data_list = []
            
            for table in tables:
                rows = table.find_all('tr')
                
                if len(rows) > 1:
                    # 检查表头
                    header_row = rows[0]
                    headers = [th.get_text(strip=True) for th in header_row.find_all(['th', 'td'])]
                    
                    # 如果表头包含预期字段
                    if any(keyword in ''.join(headers) for keyword in ['序号', '单位', '学科', '评估']):
                        for row in rows[1:]:
                            cells = row.find_all('td')
                            if len(cells) >= 5:
                                data_item = {
                                    '序号': cells[0].get_text(strip=True),
                                    '单位名称': cells[1].get_text(strip=True),
                                    '一级学科': cells[2].get_text(strip=True),
                                    '评估结果': cells[3].get_text(strip=True),
                                    '排名占比': cells[4].get_text(strip=True),
                                    '学科分类': category or '未知'
                                }
                                
                                if data_item['单位名称'] and data_item['一级学科']:
                                    data_list.append(data_item)
            
            return data_list
            
        except Exception as e:
            logger.error(f"解析HTML表格异常: {str(e)}")
            return []
    
    def crawl_all_data(self):
        """
        爬取所有数据
        """
        logger.info("开始爬取教育部第四轮学科评估数据")
        
        # 首先尝试获取所有数据
        all_data = self.get_page_data_direct()
        if all_data:
            self.all_data = all_data
            logger.info(f"获取到所有数据: {len(all_data)} 条")
            return
        
        # 如果获取不到所有数据，则分类获取
        for category_name, category_code in self.subject_categories.items():
            try:
                logger.info(f"开始爬取分类: {category_name} ({category_code})")
                
                # 尝试API方式
                api_data = self.get_api_data(category_code)
                if api_data and 'data' in api_data:
                    category_data = self.process_raw_data(api_data['data'], category_name)
                else:
                    # 尝试页面方式
                    category_data = self.get_page_data_direct(category_name)
                
                if category_data:
                    # 去重
                    new_data = []
                    for item in category_data:
                        is_duplicate = False
                        for existing_item in self.all_data:
                            if (existing_item.get('单位名称') == item.get('单位名称') and 
                                existing_item.get('一级学科') == item.get('一级学科')):
                                is_duplicate = True
                                break
                        
                        if not is_duplicate:
                            new_data.append(item)
                    
                    self.all_data.extend(new_data)
                    logger.info(f"分类 {category_name} 新增 {len(new_data)} 条数据，累计: {len(self.all_data)} 条")
                else:
                    logger.warning(f"分类 {category_name} 未获取到数据")
                
                time.sleep(2)  # 避免请求过快
                
            except Exception as e:
                logger.error(f"爬取分类 {category_name} 失败: {str(e)}")
                continue
        
        logger.info(f"爬取完成，总计: {len(self.all_data)} 条数据")
    
    def save_to_csv(self, filename: str = "subject_evaluation_api.csv"):
        """
        保存数据到CSV文件
        """
        if not self.all_data:
            logger.warning("没有数据可保存")
            return
            
        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = ['序号', '单位名称', '一级学科', '评估结果', '排名占比', '学科分类']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for item in self.all_data:
                    writer.writerow(item)
                    
            logger.info(f"数据已保存到 {filename}，共 {len(self.all_data)} 条记录")
            
        except Exception as e:
            logger.error(f"保存CSV文件失败: {str(e)}")
    
    def save_to_json(self, filename: str = "subject_evaluation_api.json"):
        """
        保存数据到JSON文件
        """
        if not self.all_data:
            logger.warning("没有数据可保存")
            return
            
        try:
            with open(filename, 'w', encoding='utf-8') as jsonfile:
                json.dump(self.all_data, jsonfile, ensure_ascii=False, indent=2)
                
            logger.info(f"数据已保存到 {filename}，共 {len(self.all_data)} 条记录")
            
        except Exception as e:
            logger.error(f"保存JSON文件失败: {str(e)}")

def main():
    """
    主函数
    """
    crawler = APICrawler()
    
    try:
        # 爬取所有数据
        crawler.crawl_all_data()
        
        # 保存数据
        crawler.save_to_csv()
        crawler.save_to_json()
        
        print(f"爬取完成！共获取 {len(crawler.all_data)} 条数据")
        
    except KeyboardInterrupt:
        logger.info("用户中断爬取")
        print("爬取被中断")
        
    except Exception as e:
        logger.error(f"爬取过程发生异常: {str(e)}")
        print(f"爬取失败: {str(e)}")

if __name__ == "__main__":
    main()
