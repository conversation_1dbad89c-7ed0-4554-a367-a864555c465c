#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学术桥教育部第四轮学科评估数据爬虫
爬取 https://www.acabridge.cn/acabridge/aca_web/xkpg4/index.html 的所有学科评估数据
使用Selenium处理动态加载的页面
"""

import requests
import json
import time
import csv
import os
from typing import List, Dict, Any
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from bs4 import BeautifulSoup

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('acabridge_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AcabridgeCrawler:
    def __init__(self):
        self.base_url = "https://www.acabridge.cn/acabridge/aca_web/xkpg4/index.html"
        self.driver = None
        self.all_data = []
        
        # 学科门类列表（根据网站实际情况）
        self.subject_categories = [
            '哲学', '经济学', '法学', '教育学', '文学', '历史学',
            '理学', '工学', '农学', '医学', '管理学', '艺术学'
        ]

    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            # 不使用无头模式，方便调试
            # chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            logger.info("Chrome驱动初始化成功")
            
        except Exception as e:
            logger.error(f"Chrome驱动初始化失败: {str(e)}")
            raise

    def close_driver(self):
        """关闭浏览器驱动"""
        if self.driver:
            self.driver.quit()
            logger.info("浏览器驱动已关闭")

    def get_subject_categories(self) -> List[Dict[str, str]]:
        """获取所有学科门类和对应的值"""
        try:
            # 等待学科门类下拉框加载
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.ID, "s1"))
            )

            # 获取学科门类下拉框
            subject_select = Select(self.driver.find_element(By.ID, "s1"))
            categories = []

            for option in subject_select.options:
                if option.get_attribute('value') != "s0":  # 跳过"请选择学科门类"
                    categories.append({
                        'name': option.text.strip(),
                        'value': option.get_attribute('value')
                    })

            logger.info(f"找到 {len(categories)} 个学科门类")
            return categories

        except Exception as e:
            logger.error(f"获取学科门类失败: {str(e)}")
            # 返回默认的学科门类和对应值
            default_categories = [
                {'name': '哲学', 'value': 's01'},
                {'name': '经济学', 'value': 's02'},
                {'name': '法学', 'value': 's03'},
                {'name': '教育学', 'value': 's04'},
                {'name': '文学', 'value': 's05'},
                {'name': '历史学', 'value': 's06'},
                {'name': '理学', 'value': 's07'},
                {'name': '工学', 'value': 's08'},
                {'name': '农学', 'value': 's09'},
                {'name': '医学', 'value': 's10'},
                {'name': '管理学', 'value': 's12'},
                {'name': '艺术学', 'value': 's13'}
            ]
            return default_categories

    def select_subject_category(self, category_value: str) -> bool:
        """选择指定学科门类"""
        try:
            # 等待学科门类下拉框
            WebDriverWait(self.driver, 20).until(
                EC.element_to_be_clickable((By.ID, "s1"))
            )

            subject_select = Select(self.driver.find_element(By.ID, "s1"))
            subject_select.select_by_value(category_value)

            logger.info(f"成功选择学科门类: {category_value}")
            time.sleep(3)  # 等待二级学科下拉框更新
            return True

        except Exception as e:
            logger.error(f"选择学科门类 {category_value} 失败: {str(e)}")
            return False

    def get_specific_subjects(self) -> List[Dict[str, str]]:
        """获取当前学科门类下的具体学科"""
        try:
            # 等待二级学科下拉框更新
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.ID, "s2"))
            )

            specific_select = Select(self.driver.find_element(By.ID, "s2"))
            subjects = []

            for option in specific_select.options:
                if option.get_attribute('value') != "s0":  # 跳过"请选择一级学科"
                    subjects.append({
                        'name': option.text.strip(),
                        'value': option.get_attribute('value')
                    })

            logger.info(f"找到 {len(subjects)} 个具体学科")
            return subjects

        except Exception as e:
            logger.error(f"获取具体学科失败: {str(e)}")
            return []

    def select_specific_subject(self, subject_value: str) -> bool:
        """选择具体学科并触发数据加载"""
        try:
            # 等待二级学科下拉框
            WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.ID, "s2"))
            )

            specific_select = Select(self.driver.find_element(By.ID, "s2"))
            specific_select.select_by_value(subject_value)

            logger.info(f"成功选择具体学科: {subject_value}")
            time.sleep(3)  # 等待数据加载
            return True

        except Exception as e:
            logger.error(f"选择具体学科 {subject_value} 失败: {str(e)}")
            return False

    def parse_table_data(self, subject: str) -> List[Dict[str, Any]]:
        """解析当前页面的表格数据"""
        try:
            # 等待表格加载
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.ID, "tbresult"))
            )

            # 获取页面源码并解析
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')

            # 查找数据表格（使用ID定位更准确）
            table = soup.find('table', id='tbresult')
            if not table:
                logger.warning("未找到数据表格")
                return []

            rows = table.find_all('tr')
            data_list = []

            # 跳过表头，从第二行开始解析
            for i, row in enumerate(rows[1:], 1):
                cells = row.find_all('td')
                if len(cells) >= 7:  # 确保有足够的列
                    data_item = {
                        '序号': cells[0].get_text(strip=True),
                        '单位名称': cells[1].get_text(strip=True),
                        '所在地区': cells[2].get_text(strip=True),
                        '学科门类': cells[3].get_text(strip=True),
                        '一级学科': cells[4].get_text(strip=True),
                        '评估结果': cells[5].get_text(strip=True),
                        '排名占比': cells[6].get_text(strip=True),
                        '查询学科': subject
                    }
                    data_list.append(data_item)

            logger.info(f"解析到 {len(data_list)} 条数据")
            return data_list

        except Exception as e:
            logger.error(f"解析表格数据异常: {str(e)}")
            return []

    def crawl_all_subjects(self):
        """爬取所有学科的数据"""
        logger.info("开始爬取所有学科数据")

        try:
            # 初始化浏览器
            self.setup_driver()

            # 访问主页面
            logger.info(f"访问主页面: {self.base_url}")
            self.driver.get(self.base_url)

            # 等待页面加载
            time.sleep(5)

            # 获取所有学科门类
            categories = self.get_subject_categories()

            for category in categories:
                try:
                    category_name = category['name']
                    category_value = category['value']

                    logger.info(f"开始爬取学科门类: {category_name}")

                    # 选择学科门类
                    if self.select_subject_category(category_value):
                        # 获取该门类下的具体学科
                        specific_subjects = self.get_specific_subjects()

                        for subject in specific_subjects:
                            try:
                                subject_name = subject['name']
                                subject_value = subject['value']

                                logger.info(f"开始爬取具体学科: {subject_name}")

                                # 选择具体学科
                                if self.select_specific_subject(subject_value):
                                    # 获取该学科的数据
                                    subject_data = self.parse_table_data(f"{category_name}-{subject_name}")
                                    self.all_data.extend(subject_data)

                                    logger.info(f"学科 {subject_name} 完成，获取 {len(subject_data)} 条数据")
                                    logger.info(f"累计数据: {len(self.all_data)} 条")
                                else:
                                    logger.warning(f"无法选择具体学科 {subject_name}")

                                # 学科间休息
                                time.sleep(2)

                            except Exception as e:
                                logger.error(f"爬取具体学科 {subject_name} 失败: {str(e)}")
                                continue
                    else:
                        logger.warning(f"无法选择学科门类 {category_name}")

                    # 门类间休息
                    time.sleep(3)

                except Exception as e:
                    logger.error(f"爬取学科门类 {category_name} 失败: {str(e)}")
                    continue

        except Exception as e:
            logger.error(f"爬取过程异常: {str(e)}")
        finally:
            self.close_driver()

        logger.info(f"所有学科爬取完成，总计: {len(self.all_data)} 条数据")

    def save_to_csv(self, filename: str = "acabridge_subject_evaluation.csv"):
        """保存数据到CSV文件"""
        if not self.all_data:
            logger.warning("没有数据可保存")
            return
        
        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = ['序号', '单位名称', '所在地区', '学科门类', '一级学科', '评估结果', '排名占比', '查询学科']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for item in self.all_data:
                    writer.writerow(item)
                
            logger.info(f"数据已保存到 {filename}，共 {len(self.all_data)} 条记录")
            
        except Exception as e:
            logger.error(f"保存CSV文件失败: {str(e)}")

    def save_to_json(self, filename: str = "acabridge_subject_evaluation.json"):
        """保存数据到JSON文件"""
        if not self.all_data:
            logger.warning("没有数据可保存")
            return
        
        try:
            with open(filename, 'w', encoding='utf-8') as jsonfile:
                json.dump(self.all_data, jsonfile, ensure_ascii=False, indent=2)
            
            logger.info(f"数据已保存到 {filename}，共 {len(self.all_data)} 条记录")
            
        except Exception as e:
            logger.error(f"保存JSON文件失败: {str(e)}")

def main():
    """主函数"""
    crawler = AcabridgeCrawler()
    
    try:
        # 爬取所有学科数据
        crawler.crawl_all_subjects()
        
        # 保存数据
        crawler.save_to_csv()
        crawler.save_to_json()
        
        print(f"爬取完成！共获取 {len(crawler.all_data)} 条数据")
        
    except KeyboardInterrupt:
        logger.info("用户中断爬取")
        print("爬取被中断")
        
    except Exception as e:
        logger.error(f"爬取过程发生异常: {str(e)}")
        print(f"爬取失败: {str(e)}")

if __name__ == "__main__":
    main()
